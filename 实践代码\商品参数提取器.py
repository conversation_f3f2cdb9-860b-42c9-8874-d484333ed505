#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品参数提取器GUI版本：获取真实认证信息后请求前十页数据并提取商品参数
功能：监听真实API请求获取认证信息，然后请求前十页数据，提取商品ID、SKUID、productSkcId和productJitMode参数
"""

from DrissionPage import Chromium
import json
import time
import threading
from datetime import datetime
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox

class ProductExtractorGUI:
    """
    商品参数提取器GUI类
    """
    def __init__(self, root):
        self.root = root
        self.root.title("商品参数提取器")
        self.root.geometry("800x600")

        # 设置全局微软雅黑字体
        self.setup_fonts()

        # 初始化变量
        self.tab = None
        self.auth_info = None
        self.is_running = False

        # 创建GUI界面
        self.create_widgets()

    def setup_fonts(self):
        """
        设置全局微软雅黑字体
        """
        import tkinter.font as tkFont

        # 设置默认字体为微软雅黑
        default_font = tkFont.nametofont("TkDefaultFont")
        default_font.configure(family="Microsoft YaHei", size=9)

        text_font = tkFont.nametofont("TkTextFont")
        text_font.configure(family="Microsoft YaHei", size=9)

        fixed_font = tkFont.nametofont("TkFixedFont")
        fixed_font.configure(family="Microsoft YaHei", size=9)

    def create_widgets(self):
        """
        创建GUI组件
        """
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 标题
        title_label = ttk.Label(main_frame, text="商品参数提取器", font=("Microsoft YaHei", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # 控制按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 开始提取按钮
        self.start_button = ttk.Button(button_frame, text="开始提取商品参数", command=self.start_extraction)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))

        # 停止按钮
        self.stop_button = ttk.Button(button_frame, text="停止提取", command=self.stop_extraction, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))

        # 清空日志按钮
        self.clear_button = ttk.Button(button_frame, text="清空日志", command=self.clear_log)
        self.clear_button.pack(side=tk.LEFT)

        # 进度条
        self.progress_var = tk.StringVar(value="准备就绪")
        progress_label = ttk.Label(main_frame, textvariable=self.progress_var)
        progress_label.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))

        self.progress_bar = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress_bar.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 日志显示区域
        log_label = ttk.Label(main_frame, text="提取日志：")
        log_label.grid(row=4, column=0, sticky=tk.W, pady=(0, 5))

        # 创建日志文本框
        self.log_text = scrolledtext.ScrolledText(main_frame, height=25, width=80)
        self.log_text.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 统计信息框架
        stats_frame = ttk.LabelFrame(main_frame, text="提取统计", padding="5")
        stats_frame.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 统计标签
        self.stats_var = tk.StringVar(value="总商品数: 0 | 成功提取: 0 | 当前页面: 0/2")
        stats_label = ttk.Label(stats_frame, textvariable=self.stats_var)
        stats_label.pack()

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(5, weight=1)

    def log_message(self, message):
        """
        在GUI日志区域显示消息
        """
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def clear_log(self):
        """
        清空日志
        """
        self.log_text.delete(1.0, tk.END)

    def start_extraction(self):
        """
        开始提取商品参数
        """
        if self.is_running:
            return

        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress_bar.start()
        self.progress_var.set("正在启动提取程序...")

        # 在新线程中运行提取程序
        thread = threading.Thread(target=self.run_extraction)
        thread.daemon = True
        thread.start()

    def stop_extraction(self):
        """
        停止提取
        """
        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress_bar.stop()
        self.progress_var.set("已停止")
        self.log_message("⏹️ 用户手动停止了提取程序")

    def run_extraction(self):
        """
        运行提取程序的主逻辑
        """
        try:
            self.log_message("=" * 60)
            self.log_message("📚 商品参数提取器启动")
            self.log_message("🎯 目标：获取真实认证信息，请求前十页数据，提取商品参数")
            self.log_message("=" * 60)

            # 步骤1：监听真实的API请求获取认证信息
            self.progress_var.set("正在监听真实API请求...")
            self.log_message("🚀 第一步：监听真实的API请求")
            print("🚀 第一步：监听真实的API请求")
            tab, real_packet = self.monitor_and_get_auth_info()

            if not self.is_running:
                return

            print(f"🔍 监听结果调试:")
            print(f"   📊 tab对象: {tab}")
            print(f"   📊 packet对象: {real_packet}")

            if tab and real_packet:
                self.tab = tab
                print("✅ 成功获取到tab和packet对象")
                # 步骤2：提取认证信息
                self.progress_var.set("正在提取认证信息...")
                self.log_message("\n🚀 第二步：提取认证信息")
                print("🚀 第二步：提取认证信息")
                self.auth_info = self.extract_auth_info(real_packet)

                print(f"🔍 认证信息提取结果:")
                print(f"   📊 auth_info: {self.auth_info}")
            else:
                print("❌ 未能获取到tab或packet对象")
                if not tab:
                    print("   ❌ tab对象为空")
                if not real_packet:
                    print("   ❌ packet对象为空")

                print("❌ 未能监听到真实的API请求")
                self.log_message("\n❌ 未能监听到真实的API请求")
                self.log_message("💡 建议：")
                self.log_message("   1. 确保页面会自动发起API请求")
                self.log_message("   2. 尝试手动刷新页面或进行操作")
                self.log_message("   3. 检查API路径是否正确")
                self.finish_extraction()
                return

            if not self.is_running:
                return

            if self.auth_info:
                print("✅ 认证信息提取成功，开始请求数据")
                # 步骤3：请求前两页数据并提取商品参数
                self.progress_var.set("正在请求商品数据...")
                self.log_message("\n🚀 第三步：请求前两页数据并提取商品参数")
                self.log_message("=" * 60)

                self.extract_all_pages()
            else:
                print("❌ 认证信息提取失败")
                self.log_message("\n❌ 无法提取认证信息")
                self.finish_extraction()

        except Exception as e:
            self.log_message(f"❌ 程序执行过程中发生错误：{e}")
            self.finish_extraction()

    def finish_extraction(self):
        """
        完成提取，重置UI状态
        """
        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress_bar.stop()
        self.progress_var.set("提取完成")

        # 停止监听器
        if self.tab:
            try:
                self.tab.listen.stop()
                self.log_message("\n✅ 网络监听器已停止")
            except:
                pass

    def monitor_and_get_auth_info(self):
        """
        监听真实的API请求，获取完整的认证信息
        """
        self.log_message("🚀 开始监听真实API请求以获取认证信息...")

        try:
            # 连接到9222端口的浏览器
            browser = Chromium(9222)
            self.log_message("✅ 已连接到浏览器")

            # 新建标签页
            new_tab = browser.new_tab()
            self.log_message("📄 新建空白标签页完成")

            # 激活标签页
            new_tab.set.activate()
            self.log_message("🎯 标签页已激活")

            # 启动网络监听器 - 监听目标API
            target_api = "visage-agent-seller/product/skc/pageQuery"
            self.log_message("🔍 启动网络监听器...")
            self.log_message(f"🎯 目标API: {target_api}")
            new_tab.listen.start(targets=target_api)
            self.log_message("✅ 网络监听器已启动")

            # 访问目标页面
            target_url = "https://agentseller.temu.com/goods/list"
            self.log_message(f"🌐 正在访问：{target_url}")

            # 访问页面
            new_tab.get(target_url)

            self.log_message("⏳ 等待页面加载...")
            new_tab.wait.load_start()
            new_tab.wait.doc_loaded()
            self.log_message("✅ 页面加载完成")

            # 等待并捕获真实的API请求
            self.log_message("\n⏳ 等待真实的API请求...")
            packet = new_tab.listen.wait(timeout=30)
            if packet:
                self.log_message("✅ 成功捕获到真实的API请求！")
                return new_tab, packet
            else:
                self.log_message("⚠️ 未捕获到API请求")
                return new_tab, None

        except Exception as e:
            self.log_message(f"❌ 监听API请求时出错：{e}")
            return None, None

    def extract_auth_info(self, packet):
        """
        从真实请求中提取认证信息
        """
        self.log_message("\n" + "=" * 60)
        self.log_message("🔍 提取真实请求的认证信息")
        self.log_message("=" * 60)

        if not packet or not packet.request:
            self.log_message("❌ 无法获取请求信息")
            return None

        # 提取重要的认证头
        auth_headers = {}
        important_headers = [
            'Anti-Content', 'mallid', 'cookie', 'authorization',
            'x-csrf-token', 'x-requested-with', 'sec-ch-ua',
            'sec-ch-ua-mobile', 'sec-ch-ua-platform', 'user-agent',
            'accept', 'accept-language', 'cache-control', 'content-type',
            'origin', 'referer', 'sec-fetch-dest', 'sec-fetch-mode', 'sec-fetch-site'
        ]

        self.log_message("📋 正在提取认证头信息...")
        for header_name in important_headers:
            for key, value in packet.request.headers.items():
                if key.lower() == header_name.lower():
                    auth_headers[key] = value
                    break

        # 提取POST数据
        post_data = None
        if packet.request.postData:
            try:
                if isinstance(packet.request.postData, dict):
                    post_data = packet.request.postData
                else:
                    post_data = json.loads(str(packet.request.postData))
                self.log_message("📝 已提取POST数据")
            except:
                post_data = str(packet.request.postData)
                self.log_message("📝 已提取POST数据（原始格式）")

        self.log_message("✅ 认证信息提取完成")

        return {
            'headers': auth_headers,
            'post_data': post_data,
            'url': packet.url
        }

    def request_page_data(self, page_num):
        """
        使用认证信息请求指定页面的数据
        """
        self.log_message(f"📡 正在请求第 {page_num} 页数据...")
        print(f"📡 正在请求第 {page_num} 页数据...")

        if not self.auth_info:
            self.log_message("❌ 没有认证信息，无法发起请求")
            print("❌ 没有认证信息，无法发起请求")
            print("💡 这通常意味着前面的认证信息获取步骤失败了")
            return None

        print(f"✅ 认证信息存在，准备发起请求")
        print(f"🔗 API URL: {self.auth_info.get('url', 'N/A')}")
        print(f"📋 请求头数量: {len(self.auth_info.get('headers', {}))}")
        print(f"📝 POST数据: {self.auth_info.get('post_data', 'N/A')}")

        # 构建请求数据，修改页码
        request_data = {
            "jitStockQuantitySection": {
                "leftValue": 0,
                "rightValue": 0
            },
            "skcJitStatus": 1,
            "page": page_num,  # 使用传入的页码
            "pageSize": 20
        }

        api_url = self.auth_info['url']
        headers = self.auth_info['headers']

        try:
            # 使用JavaScript在浏览器中发起请求
            js_code = f'''
            fetch('{api_url}', {{
                method: 'POST',
                headers: {json.dumps(headers)},
                body: JSON.stringify({json.dumps(request_data)})
            }})
            .then(response => {{
                return response.text().then(text => {{
                    return {{
                        status: response.status,
                        statusText: response.statusText,
                        body: text
                    }};
                }});
            }})
            .then(data => {{
                window.pageApiResponse = data;
                return data;
            }})
            .catch(error => {{
                window.pageApiResponse = {{
                    error: error.message
                }};
                return window.pageApiResponse;
            }});
            '''

            # 执行JavaScript代码
            self.tab.run_js(js_code)

            # 等待请求完成
            time.sleep(2)

            # 获取响应结果
            response_data = self.tab.run_js('return window.pageApiResponse;')

            # 添加详细的调试信息
            print(f"🔍 第 {page_num} 页响应数据调试:")
            print(f"   📊 响应数据类型: {type(response_data)}")
            print(f"   📊 响应数据内容: {response_data}")

            if response_data:
                if 'error' in response_data:
                    print(f"❌ JavaScript请求出错: {response_data['error']}")
                    self.log_message(f"❌ JavaScript请求出错: {response_data['error']}")
                    return None

                status = response_data.get('status')
                print(f"   📊 HTTP状态码: {status}")

                if status == 200:
                    try:
                        # 解析JSON响应
                        response_json = json.loads(response_data['body'])
                        self.log_message(f"✅ 第 {page_num} 页数据请求成功")

                        # 在Python控制台打印详细的响应信息
                        print(f"\n{'='*80}")
                        print(f"📄 第 {page_num} 页详细响应信息")
                        print(f"{'='*80}")
                        print(f"🔗 请求URL: {api_url}")
                        print(f"📊 响应状态: {response_data.get('status')}")
                        print(f"📝 响应内容:")
                        print(json.dumps(response_json, indent=2, ensure_ascii=False))
                        print(f"{'='*80}\n")

                        return response_json
                    except Exception as e:
                        self.log_message(f"❌ 第 {page_num} 页JSON解析失败：{e}")
                        print(f"❌ 第 {page_num} 页JSON解析失败：{e}")
                        print(f"📝 原始响应内容: {response_data.get('body', 'N/A')}")
                        return None
                else:
                    self.log_message(f"❌ 第 {page_num} 页请求失败，状态码: {status}")
                    print(f"❌ 第 {page_num} 页请求失败，状态码: {status}")
                    print(f"📝 响应状态文本: {response_data.get('statusText', 'N/A')}")
                    print(f"📝 响应内容: {response_data.get('body', 'N/A')}")
                    return None
            else:
                self.log_message(f"❌ 第 {page_num} 页没有获取到响应数据")
                print(f"❌ 第 {page_num} 页没有获取到响应数据")
                return None

        except Exception as e:
            self.log_message(f"❌ 第 {page_num} 页API请求失败：{e}")
            return None

    def extract_product_params(self, response_data, page_num):
        """
        从响应数据中提取商品参数
        """
        self.log_message(f"\n🔍 正在提取第 {page_num} 页的商品参数...")

        try:
            # 检查响应结构
            if not response_data or 'result' not in response_data:
                self.log_message(f"❌ 第 {page_num} 页响应数据结构异常")
                return []

            result = response_data['result']
            if 'pageItems' not in result or not result['pageItems']:
                self.log_message(f"⚠️ 第 {page_num} 页没有商品数据")
                return []

            page_items = result['pageItems']
            self.log_message(f"📦 第 {page_num} 页共找到 {len(page_items)} 个商品")

            extracted_products = []

            # 遍历每个商品
            for i, item in enumerate(page_items):
                try:
                    # 提取商品ID
                    product_id = item.get('productId', 'N/A')

                    # 提取SKUID（从第一个SKU摘要中获取）
                    sku_id = 'N/A'
                    if 'productSkuSummaries' in item and item['productSkuSummaries']:
                        sku_id = item['productSkuSummaries'][0].get('productSkuId', 'N/A')

                    # 提取productSkcId
                    product_skc_id = item.get('productSkcId', 'N/A')

                    # 提取productJitMode
                    product_jit_mode = item.get('productJitMode', 'N/A')

                    # 构建商品信息
                    product_info = {
                        'page': page_num,
                        'index': i + 1,
                        'productId': product_id,
                        'productSkuId': sku_id,
                        'productSkcId': product_skc_id,
                        'productJitMode': product_jit_mode
                    }

                    extracted_products.append(product_info)

                    # 在GUI日志中显示商品信息
                    self.log_message(f"  📦 商品 {i + 1}:")
                    self.log_message(f"     🆔 商品ID: {product_id}")
                    self.log_message(f"     🏷️  SKUID: {sku_id}")
                    self.log_message(f"     📋 productSkcId: {product_skc_id}")
                    self.log_message(f"     ⚡ productJitMode: {json.dumps(product_jit_mode, ensure_ascii=False)}")
                    self.log_message("")

                    # 在Python控制台打印详细商品信息
                    print(f"  📦 第{page_num}页-商品{i + 1}:")
                    print(f"     🆔 商品ID: {product_id}")
                    print(f"     🏷️  SKUID: {sku_id}")
                    print(f"     📋 productSkcId: {product_skc_id}")
                    print(f"     ⚡ productJitMode: {json.dumps(product_jit_mode, indent=2, ensure_ascii=False)}")
                    print(f"     📄 完整商品数据: {json.dumps(item, indent=2, ensure_ascii=False)}")
                    print("-" * 60)

                except Exception as e:
                    self.log_message(f"❌ 提取第 {i + 1} 个商品参数时出错：{e}")
                    continue

            self.log_message(f"✅ 第 {page_num} 页商品参数提取完成，共提取 {len(extracted_products)} 个商品")
            return extracted_products

        except Exception as e:
            self.log_message(f"❌ 第 {page_num} 页商品参数提取失败：{e}")
            return []

    def extract_all_pages(self):
        """
        提取前两页的所有商品参数
        """
        all_products = []  # 存储所有商品信息
        total_products = 0
        successful_pages = 0

        print("\n" + "🚀" * 30)
        print("开始提取前两页商品数据（每页20条）")
        print("🚀" * 30 + "\n")

        # 循环请求前2页
        for page in range(1, 3):
            if not self.is_running:
                break

            self.progress_var.set(f"正在处理第 {page} 页...")
            self.log_message(f"\n📄 处理第 {page} 页...")

            print(f"\n📄 开始处理第 {page} 页数据...")

            # 请求页面数据
            page_data = self.request_page_data(page)

            if page_data:
                # 提取商品参数
                products = self.extract_product_params(page_data, page)
                all_products.extend(products)
                total_products += len(products)
                successful_pages += 1

                # 更新统计信息
                self.stats_var.set(f"总商品数: {total_products} | 成功提取: {len(all_products)} | 当前页面: {page}/2")

                print(f"✅ 第 {page} 页处理完成，提取了 {len(products)} 个商品")

                # 添加延迟避免请求过快
                if page < 2 and self.is_running:
                    self.log_message("⏳ 等待2秒后请求下一页...")
                    print("⏳ 等待2秒后请求下一页...")
                    time.sleep(2)
            else:
                self.log_message(f"⚠️ 第 {page} 页数据获取失败，跳过")
                print(f"⚠️ 第 {page} 页数据获取失败，跳过")

        # 输出总结信息
        print(f"\n{'='*80}")
        print("📊 数据提取总结")
        print(f"{'='*80}")
        print(f"✅ 成功处理了 {successful_pages} 页数据")
        print(f"📦 总共提取了 {total_products} 个商品的参数")
        print(f"💾 每页20条，共获取最多40条商品数据")
        print(f"{'='*80}\n")

        self.log_message("\n" + "=" * 80)
        self.log_message("📊 数据提取总结")
        self.log_message("=" * 80)
        self.log_message(f"✅ 成功处理了 {successful_pages} 页数据")
        self.log_message(f"📦 总共提取了 {total_products} 个商品的参数")
        self.log_message(f"💾 每页20条，共获取最多40条商品数据")
        self.log_message("=" * 80)

        # 完成提取
        self.finish_extraction()


def main():
    """
    主程序：启动GUI界面
    """
    try:
        # 创建主窗口
        root = tk.Tk()

        # 创建GUI应用
        app = ProductExtractorGUI(root)

        # 启动GUI主循环
        root.mainloop()

    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败：{e}")


if __name__ == "__main__":
    main()
