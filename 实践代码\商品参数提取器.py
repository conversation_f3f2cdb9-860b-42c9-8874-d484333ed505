#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品参数提取器GUI版本：获取真实认证信息后请求前十页数据并提取商品参数
功能：监听真实API请求获取认证信息，然后请求前十页数据，提取商品ID、SKUID、productSkcId和productJitMode参数
"""

from DrissionPage import Chromium
import json
import time
import threading
from datetime import datetime
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox

class ProductExtractorGUI:
    """
    商品参数提取器GUI类
    """
    def __init__(self, root):
        self.root = root
        self.root.title("商品参数提取器")
        self.root.geometry("1000x600")

        # 设置全局微软雅黑字体
        self.setup_fonts()

        # 初始化变量
        self.tab = None
        self.auth_info = None
        self.is_running = False
        self.total_pages = 2  # 默认处理2页

        # 创建GUI界面
        self.create_widgets()

    def setup_fonts(self):
        """
        设置全局微软雅黑字体
        """
        import tkinter.font as tkFont

        # 设置默认字体为微软雅黑
        default_font = tkFont.nametofont("TkDefaultFont")
        default_font.configure(family="Microsoft YaHei", size=9)

        text_font = tkFont.nametofont("TkTextFont")
        text_font.configure(family="Microsoft YaHei", size=9)

        fixed_font = tkFont.nametofont("TkFixedFont")
        fixed_font.configure(family="Microsoft YaHei", size=9)

    def create_widgets(self):
        """
        创建GUI组件
        """
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 标题
        title_label = ttk.Label(main_frame, text="商品参数提取器", font=("Microsoft YaHei", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # 页数设置框架
        pages_frame = ttk.Frame(main_frame)
        pages_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 页数设置标签和输入框
        pages_label = ttk.Label(pages_frame, text="处理页数:")
        pages_label.pack(side=tk.LEFT, padx=(0, 5))

        self.pages_var = tk.StringVar(value="2")
        self.pages_entry = ttk.Entry(pages_frame, textvariable=self.pages_var, width=5)
        self.pages_entry.pack(side=tk.LEFT, padx=(0, 10))

        # 智能计算按钮
        self.smart_calc_button = ttk.Button(pages_frame, text="智能计算", command=self.calculate_smart_pages)
        self.smart_calc_button.pack(side=tk.LEFT, padx=(0, 10))

        pages_info_label = ttk.Label(pages_frame, text="(每页20条，点击智能计算自动获取总页数)")
        pages_info_label.pack(side=tk.LEFT)

        # 控制按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 开始提取按钮
        self.start_button = ttk.Button(button_frame, text="开始提取商品参数", command=self.start_extraction)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))

        # 停止按钮
        self.stop_button = ttk.Button(button_frame, text="停止提取", command=self.stop_extraction, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))

        # 清空日志按钮
        self.clear_button = ttk.Button(button_frame, text="清空日志", command=self.clear_log)
        self.clear_button.pack(side=tk.LEFT)

        # 进度条
        self.progress_var = tk.StringVar(value="准备就绪")
        progress_label = ttk.Label(main_frame, textvariable=self.progress_var)
        progress_label.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))

        self.progress_bar = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress_bar.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 日志显示区域
        log_label = ttk.Label(main_frame, text="提取日志：")
        log_label.grid(row=5, column=0, sticky=tk.W, pady=(0, 5))

        # 创建日志文本框
        self.log_text = scrolledtext.ScrolledText(main_frame, height=22, width=80)
        self.log_text.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 统计信息框架
        stats_frame = ttk.LabelFrame(main_frame, text="提取统计", padding="5")
        stats_frame.grid(row=7, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 统计标签
        self.stats_var = tk.StringVar(value="总商品数: 0 | 成功提取: 0 | JIT签署: 0 | 当前页面: 0/2")
        stats_label = ttk.Label(stats_frame, textvariable=self.stats_var)
        stats_label.pack()

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(6, weight=1)

    def log_message(self, message):
        """
        在GUI日志区域显示消息
        """
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def clear_log(self):
        """
        清空日志
        """
        self.log_text.delete(1.0, tk.END)

    def calculate_smart_pages(self):
        """
        智能计算页数：通过API获取商品总数并计算页数
        """
        if self.is_running:
            messagebox.showwarning("提示", "程序正在运行中，请先停止后再计算页数")
            return

        self.smart_calc_button.config(state=tk.DISABLED)
        self.start_button.config(state=tk.DISABLED)
        self.log_message("🧠 开始智能计算页数...")

        # 在新线程中执行计算
        thread = threading.Thread(target=self.run_smart_calculation)
        thread.daemon = True
        thread.start()

    def run_smart_calculation(self):
        """
        在后台线程中运行智能计算
        """
        try:
            # 首先需要获取认证信息
            if not self.auth_info:
                self.log_message("📡 正在获取认证信息...")
                tab, packet = self.monitor_and_get_auth_info()
                if tab and packet:
                    self.tab = tab
                    self.auth_info = self.extract_auth_info(packet)

            if not self.auth_info:
                self.log_message("❌ 无法获取认证信息，智能计算失败")
                self.smart_calc_button.config(state=tk.NORMAL)
                return

            # 获取商品总数
            total_count = self.get_total_product_count()
            if total_count > 0:
                # 计算页数（每页20条）
                total_pages = (total_count + 19) // 20  # 向上取整

                # 更新页数输入框
                self.pages_var.set(str(total_pages))

                self.log_message(f"✅ 智能计算完成：总商品数 {total_count}，建议处理 {total_pages} 页")
                print(f"✅ 智能计算完成：总商品数 {total_count}，建议处理 {total_pages} 页")

                # 显示确认对话框
                result = messagebox.askyesno("智能计算结果",
                    f"检测到总共 {total_count} 个商品\n"
                    f"建议处理 {total_pages} 页（每页20条）\n\n"
                    f"是否使用此页数设置？")

                if not result:
                    self.pages_var.set("2")  # 恢复默认值
                    self.log_message("🔄 用户取消，恢复默认页数设置")
            else:
                self.log_message("❌ 无法获取商品总数，智能计算失败")

        except Exception as e:
            self.log_message(f"❌ 智能计算过程中出错：{e}")
            print(f"❌ 智能计算过程中出错：{e}")
        finally:
            self.smart_calc_button.config(state=tk.NORMAL)
            self.start_button.config(state=tk.NORMAL)

    def get_total_product_count(self):
        """
        获取商品总数
        """
        if not self.auth_info:
            return 0

        # 构建统计请求数据
        count_data = {
            "jitStockQuantitySection": {
                "leftValue": 0,
                "rightValue": 0
            },
            "skcJitStatus": 1,
            "page": 1,
            "pageSize": 20
        }

        # 统计API地址
        count_url = "https://agentseller.temu.com/visage-agent-seller/product/skc/countStatus"
        headers = self.auth_info['headers']

        try:
            self.log_message("📊 正在获取商品总数...")
            print("📊 正在获取商品总数...")

            # 使用JavaScript在浏览器中发起统计请求
            js_code = f'''
            fetch('{count_url}', {{
                method: 'POST',
                headers: {json.dumps(headers)},
                body: JSON.stringify({json.dumps(count_data)})
            }})
            .then(response => {{
                return response.text().then(text => {{
                    return {{
                        status: response.status,
                        statusText: response.statusText,
                        body: text
                    }};
                }});
            }})
            .then(data => {{
                window.countApiResponse = data;
                return data;
            }})
            .catch(error => {{
                window.countApiResponse = {{
                    error: error.message
                }};
                return window.countApiResponse;
            }});
            '''

            # 执行JavaScript代码
            self.tab.run_js(js_code)

            # 等待请求完成
            time.sleep(2)

            # 获取响应结果
            response_data = self.tab.run_js('return window.countApiResponse;')

            if response_data and response_data.get('status') == 200:
                try:
                    # 解析JSON响应
                    response_json = json.loads(response_data['body'])

                    # 检查响应结构并提取总数
                    if response_json.get('success') and 'result' in response_json:
                        result = response_json['result']
                        if 'skcTopStatusCountList' in result and result['skcTopStatusCountList']:
                            # 查找skcTopStatus为0的项目（通常是全部商品）
                            for status_item in result['skcTopStatusCountList']:
                                if status_item.get('skcTopStatus') == 0:
                                    total_count = status_item.get('skcTotal', 0)
                                    self.log_message(f"📊 获取到商品总数：{total_count}")
                                    print(f"📊 获取到商品总数：{total_count}")
                                    return total_count

                    self.log_message("❌ 响应数据结构异常，无法获取商品总数")
                    print("❌ 响应数据结构异常，无法获取商品总数")
                    return 0

                except Exception as e:
                    self.log_message(f"❌ 统计响应解析失败：{e}")
                    print(f"❌ 统计响应解析失败：{e}")
                    return 0
            else:
                self.log_message(f"❌ 统计请求失败，状态码: {response_data.get('status') if response_data else 'None'}")
                print(f"❌ 统计请求失败，状态码: {response_data.get('status') if response_data else 'None'}")
                return 0

        except Exception as e:
            self.log_message(f"❌ 获取商品总数异常：{e}")
            print(f"❌ 获取商品总数异常：{e}")
            return 0

    def start_extraction(self):
        """
        开始提取商品参数
        """
        if self.is_running:
            return

        # 获取用户设置的页数
        try:
            self.total_pages = int(self.pages_var.get())
            if self.total_pages < 1:
                messagebox.showerror("错误", "页数必须大于0")
                return
        except ValueError:
            messagebox.showerror("错误", "请输入有效的页数")
            return

        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.pages_entry.config(state=tk.DISABLED)
        self.progress_bar.start()
        self.progress_var.set("正在启动提取程序...")

        # 更新统计显示
        self.stats_var.set(f"总商品数: 0 | 成功提取: 0 | JIT签署: 0 | 当前页面: 0/{self.total_pages}")

        # 在新线程中运行提取程序
        thread = threading.Thread(target=self.run_extraction)
        thread.daemon = True
        thread.start()

    def stop_extraction(self):
        """
        停止提取
        """
        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.pages_entry.config(state=tk.NORMAL)
        self.progress_bar.stop()
        self.progress_var.set("已停止")
        self.log_message("⏹️ 用户手动停止了提取程序")

    def run_extraction(self):
        """
        运行提取程序的主逻辑
        """
        try:
            self.log_message("=" * 60)
            self.log_message("📚 商品参数提取器启动")
            self.log_message("🎯 目标：获取真实认证信息，请求前十页数据，提取商品参数")
            self.log_message("=" * 60)

            # 步骤1：监听真实的API请求获取认证信息
            self.progress_var.set("正在监听真实API请求...")
            self.log_message("🚀 第一步：监听真实的API请求")
            print("🚀 第一步：监听真实的API请求")
            tab, real_packet = self.monitor_and_get_auth_info()

            if not self.is_running:
                return

            print(f"🔍 监听结果调试:")
            print(f"   📊 tab对象: {tab}")
            print(f"   📊 packet对象: {real_packet}")

            if tab and real_packet:
                self.tab = tab
                print("✅ 成功获取到tab和packet对象")
                # 步骤2：提取认证信息
                self.progress_var.set("正在提取认证信息...")
                self.log_message("\n🚀 第二步：提取认证信息")
                print("🚀 第二步：提取认证信息")
                self.auth_info = self.extract_auth_info(real_packet)

                print(f"🔍 认证信息提取结果:")
                print(f"   📊 auth_info: {self.auth_info}")
            else:
                print("❌ 未能获取到tab或packet对象")
                if not tab:
                    print("   ❌ tab对象为空")
                if not real_packet:
                    print("   ❌ packet对象为空")

                print("❌ 未能监听到真实的API请求")
                self.log_message("\n❌ 未能监听到真实的API请求")
                self.log_message("💡 建议：")
                self.log_message("   1. 确保页面会自动发起API请求")
                self.log_message("   2. 尝试手动刷新页面或进行操作")
                self.log_message("   3. 检查API路径是否正确")
                self.finish_extraction()
                return

            if not self.is_running:
                return

            if self.auth_info:
                print("✅ 认证信息提取成功，开始请求数据")
                # 步骤3：请求数据并提取商品参数
                self.progress_var.set("正在请求商品数据...")
                self.log_message(f"\n🚀 第三步：请求前{self.total_pages}页数据并提取商品参数")
                self.log_message("=" * 60)

                self.extract_all_pages()
            else:
                print("❌ 认证信息提取失败")
                self.log_message("\n❌ 无法提取认证信息")
                self.finish_extraction()

        except Exception as e:
            self.log_message(f"❌ 程序执行过程中发生错误：{e}")
            self.finish_extraction()

    def finish_extraction(self):
        """
        完成提取，重置UI状态
        """
        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.pages_entry.config(state=tk.NORMAL)
        self.progress_bar.stop()
        self.progress_var.set("提取完成")

        # 停止监听器
        if self.tab:
            try:
                self.tab.listen.stop()
                self.log_message("\n✅ 网络监听器已停止")
            except:
                pass

    def monitor_and_get_auth_info(self):
        """
        监听真实的API请求，获取完整的认证信息
        """
        self.log_message("🚀 开始监听真实API请求以获取认证信息...")

        try:
            # 连接到9222端口的浏览器
            browser = Chromium(9222)
            self.log_message("✅ 已连接到浏览器")

            # 新建标签页
            new_tab = browser.new_tab()
            self.log_message("📄 新建空白标签页完成")

            # 激活标签页
            new_tab.set.activate()
            self.log_message("🎯 标签页已激活")

            # 启动网络监听器 - 监听目标API
            target_api = "visage-agent-seller/product/skc/pageQuery"
            self.log_message("🔍 启动网络监听器...")
            self.log_message(f"🎯 目标API: {target_api}")
            new_tab.listen.start(targets=target_api)
            self.log_message("✅ 网络监听器已启动")

            # 访问目标页面
            target_url = "https://agentseller.temu.com/goods/list"
            self.log_message(f"🌐 正在访问：{target_url}")

            # 访问页面
            new_tab.get(target_url)

            self.log_message("⏳ 等待页面加载...")
            new_tab.wait.load_start()
            new_tab.wait.doc_loaded()
            self.log_message("✅ 页面加载完成")

            # 等待并捕获真实的API请求
            self.log_message("\n⏳ 等待真实的API请求...")
            packet = new_tab.listen.wait(timeout=30)
            if packet:
                self.log_message("✅ 成功捕获到真实的API请求！")
                return new_tab, packet
            else:
                self.log_message("⚠️ 未捕获到API请求")
                return new_tab, None

        except Exception as e:
            self.log_message(f"❌ 监听API请求时出错：{e}")
            return None, None

    def extract_auth_info(self, packet):
        """
        从真实请求中提取认证信息
        """
        self.log_message("\n" + "=" * 60)
        self.log_message("🔍 提取真实请求的认证信息")
        self.log_message("=" * 60)

        if not packet or not packet.request:
            self.log_message("❌ 无法获取请求信息")
            return None

        # 提取重要的认证头
        auth_headers = {}
        important_headers = [
            'Anti-Content', 'mallid', 'cookie', 'authorization',
            'x-csrf-token', 'x-requested-with', 'sec-ch-ua',
            'sec-ch-ua-mobile', 'sec-ch-ua-platform', 'user-agent',
            'accept', 'accept-language', 'cache-control', 'content-type',
            'origin', 'referer', 'sec-fetch-dest', 'sec-fetch-mode', 'sec-fetch-site'
        ]

        self.log_message("📋 正在提取认证头信息...")
        for header_name in important_headers:
            for key, value in packet.request.headers.items():
                if key.lower() == header_name.lower():
                    auth_headers[key] = value
                    break

        # 提取POST数据
        post_data = None
        if packet.request.postData:
            try:
                if isinstance(packet.request.postData, dict):
                    post_data = packet.request.postData
                else:
                    post_data = json.loads(str(packet.request.postData))
                self.log_message("📝 已提取POST数据")
            except:
                post_data = str(packet.request.postData)
                self.log_message("📝 已提取POST数据（原始格式）")

        self.log_message("✅ 认证信息提取完成")

        return {
            'headers': auth_headers,
            'post_data': post_data,
            'url': packet.url
        }

    def request_page_data(self, page_num):
        """
        使用认证信息请求指定页面的数据
        """
        self.log_message(f"📡 正在请求第 {page_num} 页数据...")
        print(f"📡 正在请求第 {page_num} 页数据...")

        if not self.auth_info:
            self.log_message("❌ 没有认证信息，无法发起请求")
            print("❌ 没有认证信息，无法发起请求")
            print("💡 这通常意味着前面的认证信息获取步骤失败了")
            return None

        # 构建请求数据，修改页码
        request_data = {
            "jitStockQuantitySection": {
                "leftValue": 0,
                "rightValue": 0
            },
            "skcJitStatus": 1,
            "page": page_num,  # 使用传入的页码
            "pageSize": 20
        }

        print(f"✅ 认证信息存在，准备发起请求")
        print(f"🔗 API URL: {self.auth_info.get('url', 'N/A')}")
        print(f"📋 请求头数量: {len(self.auth_info.get('headers', {}))}")
        print(f"📝 当前请求数据: {request_data}")

        api_url = self.auth_info['url']
        headers = self.auth_info['headers']

        try:
            # 使用JavaScript在浏览器中发起请求
            js_code = f'''
            fetch('{api_url}', {{
                method: 'POST',
                headers: {json.dumps(headers)},
                body: JSON.stringify({json.dumps(request_data)})
            }})
            .then(response => {{
                return response.text().then(text => {{
                    return {{
                        status: response.status,
                        statusText: response.statusText,
                        body: text
                    }};
                }});
            }})
            .then(data => {{
                window.pageApiResponse = data;
                return data;
            }})
            .catch(error => {{
                window.pageApiResponse = {{
                    error: error.message
                }};
                return window.pageApiResponse;
            }});
            '''

            # 执行JavaScript代码
            self.tab.run_js(js_code)

            # 等待请求完成
            time.sleep(2)

            # 获取响应结果
            response_data = self.tab.run_js('return window.pageApiResponse;')

            # 添加详细的调试信息
            if response_data:
                if 'error' in response_data:
                    print(f"❌ JavaScript请求出错: {response_data['error']}")
                    self.log_message(f"❌ JavaScript请求出错: {response_data['error']}")
                    return None

                status = response_data.get('status')
                response_body = response_data.get('body', '')

                # 只有当响应内容少于1000字符时才打印（可能出问题了）
                if len(response_body) < 1000:
                    print(f"🔍 第 {page_num} 页响应数据调试（内容较短，可能有问题）:")
                    print(f"   📊 响应数据类型: {type(response_data)}")
                    print(f"   📊 响应数据内容: {response_data}")
                    print(f"   📊 HTTP状态码: {status}")
                else:
                    print(f"🔍 第 {page_num} 页响应数据: 状态码={status}, 内容长度={len(response_body)}字符")

                if status == 200:
                    try:
                        # 解析JSON响应
                        response_json = json.loads(response_data['body'])
                        self.log_message(f"✅ 第 {page_num} 页数据请求成功")

                        # 在Python控制台打印简要信息
                        print(f"✅ 第 {page_num} 页数据请求成功，状态码: {response_data.get('status')}")

                        return response_json
                    except Exception as e:
                        self.log_message(f"❌ 第 {page_num} 页JSON解析失败：{e}")
                        print(f"❌ 第 {page_num} 页JSON解析失败：{e}")
                        return None
                else:
                    self.log_message(f"❌ 第 {page_num} 页请求失败，状态码: {status}")
                    print(f"❌ 第 {page_num} 页请求失败，状态码: {status}")
                    return None
            else:
                self.log_message(f"❌ 第 {page_num} 页没有获取到响应数据")
                print(f"❌ 第 {page_num} 页没有获取到响应数据")
                return None

        except Exception as e:
            self.log_message(f"❌ 第 {page_num} 页API请求失败：{e}")
            return None

    def sign_jit_agreement(self, product_id, product_skc_id):
        """
        为商品签署JIT协议
        """
        if not self.auth_info:
            print("❌ 没有认证信息，无法签署JIT协议")
            return False

        # 构建签署请求数据
        sign_data = {
            "skcList": [
                {
                    "productId": product_id,
                    "productSkcId": product_skc_id
                }
            ]
        }

        # JIT签署API地址
        sign_url = "https://agentseller.temu.com/visage-agent-seller/product/agreement/batch/sign"
        headers = self.auth_info['headers']

        try:
            print(f"🔄 正在为商品 {product_id} 签署JIT协议...")
            self.log_message(f"🔄 正在为商品 {product_id} 签署JIT协议...")

            # 使用JavaScript在浏览器中发起签署请求
            js_code = f'''
            fetch('{sign_url}', {{
                method: 'POST',
                headers: {json.dumps(headers)},
                body: JSON.stringify({json.dumps(sign_data)})
            }})
            .then(response => {{
                return response.text().then(text => {{
                    return {{
                        status: response.status,
                        statusText: response.statusText,
                        body: text
                    }};
                }});
            }})
            .then(data => {{
                window.jitSignResponse = data;
                return data;
            }})
            .catch(error => {{
                window.jitSignResponse = {{
                    error: error.message
                }};
                return window.jitSignResponse;
            }});
            '''

            # 执行JavaScript代码
            self.tab.run_js(js_code)

            # 等待请求完成
            time.sleep(1)

            # 获取响应结果
            response_data = self.tab.run_js('return window.jitSignResponse;')

            if response_data and response_data.get('status') == 200:
                try:
                    # 解析JSON响应
                    response_json = json.loads(response_data['body'])

                    # 检查签署结果
                    if response_json.get('success') and response_json.get('result', {}).get('successNum', 0) > 0:
                        print(f"✅ 商品 {product_id} JIT协议签署成功")
                        self.log_message(f"✅ 商品 {product_id} JIT协议签署成功")

                        # 打印简要响应信息
                        print(f"📄 JIT签署成功")
                        return True
                    else:
                        print(f"❌ 商品 {product_id} JIT协议签署失败")
                        self.log_message(f"❌ 商品 {product_id} JIT协议签署失败")
                        print(f"📄 JIT签署失败")
                        return False

                except Exception as e:
                    print(f"❌ JIT签署响应解析失败：{e}")
                    self.log_message(f"❌ JIT签署响应解析失败：{e}")
                    return False
            else:
                print(f"❌ JIT签署请求失败，状态码: {response_data.get('status') if response_data else 'None'}")
                self.log_message(f"❌ JIT签署请求失败")
                return False

        except Exception as e:
            print(f"❌ JIT签署请求异常：{e}")
            self.log_message(f"❌ JIT签署请求异常：{e}")
            return False

    def extract_product_params(self, response_data, page_num, start_index=1):
        """
        从响应数据中提取商品参数并处理JIT签署
        """
        self.log_message(f"🔍 正在提取第 {page_num} 页的商品参数...")

        try:
            # 检查响应结构
            if not response_data or 'result' not in response_data:
                self.log_message(f"❌ 第 {page_num} 页响应数据结构异常")
                return [], 0

            result = response_data['result']
            if 'pageItems' not in result or not result['pageItems']:
                self.log_message(f"⚠️ 第 {page_num} 页没有商品数据")
                return [], 0

            page_items = result['pageItems']
            self.log_message(f"📦 第 {page_num} 页共找到 {len(page_items)} 个商品")

            extracted_products = []
            jit_signed_count = 0

            # 遍历每个商品
            for i, item in enumerate(page_items):
                try:
                    # 提取商品ID
                    product_id = item.get('productId', 'N/A')

                    # 提取SKUID（从第一个SKU摘要中获取）
                    sku_id = 'N/A'
                    if 'productSkuSummaries' in item and item['productSkuSummaries']:
                        sku_id = item['productSkuSummaries'][0].get('productSkuId', 'N/A')

                    # 提取productSkcId
                    product_skc_id = item.get('productSkcId', 'N/A')

                    # 提取productJitMode
                    product_jit_mode = item.get('productJitMode', 'N/A')

                    # 检查是否需要签署JIT协议
                    need_jit_sign = False
                    if isinstance(product_jit_mode, dict) and product_jit_mode.get('quickSellAgtSignStatus') is None:
                        need_jit_sign = True
                        self.log_message(f"⚠️ 商品 {product_id} 未签署JIT规则，准备自动签署")
                        print(f"⚠️ 商品 {product_id} 未签署JIT规则，准备自动签署")

                        # 执行JIT签署
                        if self.sign_jit_agreement(product_id, product_skc_id):
                            jit_signed_count += 1
                            self.log_message(f"✅ 商品 {product_id} JIT协议签署成功")
                            print(f"✅ 商品 {product_id} JIT协议签署成功")
                        else:
                            self.log_message(f"❌ 商品 {product_id} JIT协议签署失败")
                            print(f"❌ 商品 {product_id} JIT协议签署失败")

                    # 计算全局商品编号
                    global_index = start_index + i

                    # 构建商品信息
                    product_info = {
                        'page': page_num,
                        'index': global_index,
                        'productId': product_id,
                        'productSkuId': sku_id,
                        'productSkcId': product_skc_id,
                        'productJitMode': product_jit_mode,
                        'jitSigned': need_jit_sign
                    }

                    extracted_products.append(product_info)

                    # 在GUI日志中显示商品信息（一行显示）
                    jit_status = "✅已签署" if not need_jit_sign else "🔄已处理"
                    jit_mode_str = json.dumps(product_jit_mode, ensure_ascii=False, separators=(',', ':'))
                    self.log_message(f"�商品{global_index}: ID={product_id}, SKUID={sku_id}, SkcID={product_skc_id}, JIT={jit_status}, Mode={jit_mode_str}")

                    # 在Python控制台打印简要商品信息
                    print(f"📦 商品{global_index}: ID={product_id}, SKUID={sku_id}, SkcID={product_skc_id}, JIT={jit_status}")

                except Exception as e:
                    self.log_message(f"❌ 提取第 {i + 1} 个商品参数时出错：{e}")
                    continue

            self.log_message(f"✅ 第 {page_num} 页商品参数提取完成，共提取 {len(extracted_products)} 个商品，JIT签署 {jit_signed_count} 个")
            return extracted_products, jit_signed_count

        except Exception as e:
            self.log_message(f"❌ 第 {page_num} 页商品参数提取失败：{e}")
            return [], 0

    def extract_all_pages(self):
        """
        提取指定页数的所有商品参数
        """
        all_products = []  # 存储所有商品信息
        total_products = 0
        total_jit_signed = 0
        successful_pages = 0

        print(f"🚀 开始提取前{self.total_pages}页商品数据（每页20条）")

        # 循环请求指定页数
        for page in range(1, self.total_pages + 1):
            if not self.is_running:
                break

            self.progress_var.set(f"正在处理第 {page} 页...")
            self.log_message(f"\n📄 处理第 {page} 页...")

            print(f"📄 开始处理第 {page} 页数据...")

            # 请求页面数据
            page_data = self.request_page_data(page)

            if page_data:
                # 计算当前页商品的起始编号
                start_index = total_products + 1

                # 提取商品参数并处理JIT签署
                products, jit_signed = self.extract_product_params(page_data, page, start_index)
                all_products.extend(products)
                total_products += len(products)
                total_jit_signed += jit_signed
                successful_pages += 1

                # 更新统计信息
                self.stats_var.set(f"总商品数: {total_products} | 成功提取: {len(all_products)} | JIT签署: {total_jit_signed} | 当前页面: {page}/{self.total_pages}")

                print(f"✅ 第 {page} 页处理完成，提取了 {len(products)} 个商品，JIT签署 {jit_signed} 个")

                # 添加延迟避免请求过快
                if page < self.total_pages and self.is_running:
                    self.log_message("⏳ 等待2秒后请求下一页...")
                    print("⏳ 等待2秒后请求下一页...")
                    time.sleep(2)
            else:
                self.log_message(f"⚠️ 第 {page} 页数据获取失败，跳过")
                print(f"⚠️ 第 {page} 页数据获取失败，跳过")

        # 输出总结信息
        print(f"📊 提取完成: 处理{successful_pages}页, 提取{total_products}个商品, JIT签署{total_jit_signed}个")

        self.log_message("\n" + "=" * 80)
        self.log_message("📊 数据提取总结")
        self.log_message("=" * 80)
        self.log_message(f"✅ 成功处理了 {successful_pages} 页数据")
        self.log_message(f"📦 总共提取了 {total_products} 个商品的参数")
        self.log_message(f"🎯 自动签署了 {total_jit_signed} 个商品的JIT协议")
        self.log_message(f"💾 每页20条，共获取最多{self.total_pages * 20}条商品数据")
        self.log_message("=" * 80)

        # 完成提取
        self.finish_extraction()


def main():
    """
    主程序：启动GUI界面
    """
    try:
        # 创建主窗口
        root = tk.Tk()

        # 创建GUI应用
        app = ProductExtractorGUI(root)

        # 启动GUI主循环
        root.mainloop()

    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败：{e}")


if __name__ == "__main__":
    main()
