#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品参数提取器：获取真实认证信息后请求前十页数据并提取商品参数
功能：监听真实API请求获取认证信息，然后请求前十页数据，提取商品ID、SKUID、productSkcId和productJitMode参数
"""

from DrissionPage import Chromium
import json
import time
from datetime import datetime

def monitor_and_get_auth_info():
    """
    监听真实的API请求，获取完整的认证信息
    """
    print("🚀 开始监听真实API请求以获取认证信息...")
    
    # 连接到9222端口的浏览器
    browser = Chromium(9222)
    print("✅ 已连接到浏览器")
    
    # 新建标签页
    new_tab = browser.new_tab()
    print("📄 新建空白标签页完成")
    
    # 激活标签页
    new_tab.set.activate()
    print("🎯 标签页已激活")
    
    # 启动网络监听器 - 监听目标API
    target_api = "visage-agent-seller/product/skc/pageQuery"
    print("🔍 启动网络监听器...")
    print(f"🎯 目标API: {target_api}")
    new_tab.listen.start(targets=target_api)
    print("✅ 网络监听器已启动")
    
    # 访问目标页面
    target_url = "https://agentseller.temu.com/goods/list"
    print(f"🌐 正在访问：{target_url}")
    
    # 访问页面
    new_tab.get(target_url)
    
    print("⏳ 等待页面加载...")
    new_tab.wait.load_start()
    new_tab.wait.doc_loaded()
    print("✅ 页面加载完成")
    
    # 等待并捕获真实的API请求
    print("\n⏳ 等待真实的API请求...")
    try:
        packet = new_tab.listen.wait(timeout=30)
        if packet:
            print("✅ 成功捕获到真实的API请求！")
            return new_tab, packet
        else:
            print("⚠️ 未捕获到API请求")
            return new_tab, None
    except Exception as e:
        print(f"❌ 监听API请求时出错：{e}")
        return new_tab, None

def extract_auth_info(packet):
    """
    从真实请求中提取认证信息
    """
    print("\n" + "=" * 60)
    print("🔍 提取真实请求的认证信息")
    print("=" * 60)

    if not packet or not packet.request:
        print("❌ 无法获取请求信息")
        return None

    # 提取重要的认证头
    auth_headers = {}
    important_headers = [
        'Anti-Content', 'mallid', 'cookie', 'authorization',
        'x-csrf-token', 'x-requested-with', 'sec-ch-ua',
        'sec-ch-ua-mobile', 'sec-ch-ua-platform', 'user-agent',
        'accept', 'accept-language', 'cache-control', 'content-type',
        'origin', 'referer', 'sec-fetch-dest', 'sec-fetch-mode', 'sec-fetch-site'
    ]

    print("📋 正在提取认证头信息...")
    for header_name in important_headers:
        for key, value in packet.request.headers.items():
            if key.lower() == header_name.lower():
                auth_headers[key] = value
                break

    # 提取POST数据
    post_data = None
    if packet.request.postData:
        try:
            if isinstance(packet.request.postData, dict):
                post_data = packet.request.postData
            else:
                post_data = json.loads(str(packet.request.postData))
            print("📝 已提取POST数据")
        except:
            post_data = str(packet.request.postData)
            print("📝 已提取POST数据（原始格式）")

    print("✅ 认证信息提取完成")

    return {
        'headers': auth_headers,
        'post_data': post_data,
        'url': packet.url
    }

def request_page_data(tab, auth_info, page_num):
    """
    使用认证信息请求指定页面的数据
    """
    print(f"📡 正在请求第 {page_num} 页数据...")
    
    if not auth_info:
        print("❌ 没有认证信息，无法发起请求")
        return None
    
    # 构建请求数据，修改页码
    request_data = {
        "jitStockQuantitySection": {
            "leftValue": 0,
            "rightValue": 0
        },
        "skcJitStatus": 1,
        "page": page_num,  # 使用传入的页码
        "pageSize": 20
    }
    
    api_url = auth_info['url']
    headers = auth_info['headers']
    
    try:
        # 使用JavaScript在浏览器中发起请求
        js_code = f'''
        fetch('{api_url}', {{
            method: 'POST',
            headers: {json.dumps(headers)},
            body: JSON.stringify({json.dumps(request_data)})
        }})
        .then(response => {{
            return response.text().then(text => {{
                return {{
                    status: response.status,
                    statusText: response.statusText,
                    body: text
                }};
            }});
        }})
        .then(data => {{
            window.pageApiResponse = data;
            return data;
        }})
        .catch(error => {{
            window.pageApiResponse = {{
                error: error.message
            }};
            return window.pageApiResponse;
        }});
        '''
        
        # 执行JavaScript代码
        tab.run_js(js_code)
        
        # 等待请求完成
        time.sleep(2)
        
        # 获取响应结果
        response_data = tab.run_js('return window.pageApiResponse;')
        
        if response_data and response_data.get('status') == 200:
            try:
                # 解析JSON响应
                response_json = json.loads(response_data['body'])
                print(f"✅ 第 {page_num} 页数据请求成功")
                return response_json
            except Exception as e:
                print(f"❌ 第 {page_num} 页JSON解析失败：{e}")
                return None
        else:
            print(f"❌ 第 {page_num} 页请求失败")
            return None
            
    except Exception as e:
        print(f"❌ 第 {page_num} 页API请求失败：{e}")
        return None

def extract_product_params(response_data, page_num):
    """
    从响应数据中提取商品参数
    """
    print(f"\n🔍 正在提取第 {page_num} 页的商品参数...")
    
    try:
        # 检查响应结构
        if not response_data or 'result' not in response_data:
            print(f"❌ 第 {page_num} 页响应数据结构异常")
            return []
        
        result = response_data['result']
        if 'pageItems' not in result or not result['pageItems']:
            print(f"⚠️ 第 {page_num} 页没有商品数据")
            return []
        
        page_items = result['pageItems']
        print(f"📦 第 {page_num} 页共找到 {len(page_items)} 个商品")
        
        extracted_products = []
        
        # 遍历每个商品
        for i, item in enumerate(page_items):
            try:
                # 提取商品ID
                product_id = item.get('productId', 'N/A')
                
                # 提取SKUID（从第一个SKU摘要中获取）
                sku_id = 'N/A'
                if 'productSkuSummaries' in item and item['productSkuSummaries']:
                    sku_id = item['productSkuSummaries'][0].get('productSkuId', 'N/A')
                
                # 提取productSkcId
                product_skc_id = item.get('productSkcId', 'N/A')
                
                # 提取productJitMode
                product_jit_mode = item.get('productJitMode', 'N/A')
                
                # 构建商品信息
                product_info = {
                    'page': page_num,
                    'index': i + 1,
                    'productId': product_id,
                    'productSkuId': sku_id,
                    'productSkcId': product_skc_id,
                    'productJitMode': product_jit_mode
                }
                
                extracted_products.append(product_info)
                
                # 打印商品信息到控制台
                print(f"  📦 商品 {i + 1}:")
                print(f"     🆔 商品ID: {product_id}")
                print(f"     🏷️  SKUID: {sku_id}")
                print(f"     📋 productSkcId: {product_skc_id}")
                print(f"     ⚡ productJitMode: {json.dumps(product_jit_mode, ensure_ascii=False)}")
                print()
                
            except Exception as e:
                print(f"❌ 提取第 {i + 1} 个商品参数时出错：{e}")
                continue
        
        print(f"✅ 第 {page_num} 页商品参数提取完成，共提取 {len(extracted_products)} 个商品")
        return extracted_products
        
    except Exception as e:
        print(f"❌ 第 {page_num} 页商品参数提取失败：{e}")
        return []

if __name__ == "__main__":
    """
    主程序：获取认证信息，请求前十页数据，提取商品参数
    """
    print("=" * 80)
    print("📚 商品参数提取器")
    print("🎯 目标：获取真实认证信息，请求前十页数据，提取商品参数")
    print("=" * 80)
    
    try:
        # 步骤1：监听真实的API请求获取认证信息
        print("🚀 第一步：监听真实的API请求")
        tab, real_packet = monitor_and_get_auth_info()
        
        if tab and real_packet:
            # 步骤2：提取认证信息
            print("\n🚀 第二步：提取认证信息")
            auth_info = extract_auth_info(real_packet)
            
            if auth_info:
                # 步骤3：请求前十页数据并提取商品参数
                print("\n🚀 第三步：请求前十页数据并提取商品参数")
                print("=" * 60)
                
                all_products = []  # 存储所有商品信息
                
                # 循环请求前10页
                for page in range(1, 11):
                    print(f"\n📄 处理第 {page} 页...")
                    
                    # 请求页面数据
                    page_data = request_page_data(tab, auth_info, page)
                    
                    if page_data:
                        # 提取商品参数
                        products = extract_product_params(page_data, page)
                        all_products.extend(products)
                        
                        # 添加延迟避免请求过快
                        if page < 10:
                            print("⏳ 等待2秒后请求下一页...")
                            time.sleep(2)
                    else:
                        print(f"⚠️ 第 {page} 页数据获取失败，跳过")
                
                # 输出总结信息
                print("\n" + "=" * 80)
                print("📊 数据提取总结")
                print("=" * 80)
                print(f"✅ 成功处理了前10页数据")
                print(f"📦 总共提取了 {len(all_products)} 个商品的参数")
                print(f"💾 所有商品参数已在上方控制台中详细显示")
                print("=" * 80)
                
            else:
                print("\n❌ 无法提取认证信息")
        else:
            print("\n❌ 未能监听到真实的API请求")
            print("💡 建议：")
            print("   1. 确保页面会自动发起API请求")
            print("   2. 尝试手动刷新页面或进行操作")
            print("   3. 检查API路径是否正确")
        
        # 停止监听器
        if tab:
            tab.listen.stop()
            print("\n✅ 网络监听器已停止")
        
    except Exception as e:
        print(f"❌ 程序执行过程中发生错误：{e}")
        print("💡 请检查：")
        print("   1. 浏览器是否正确安装并可以启动")
        print("   2. 网络连接是否正常")
        print("   3. 目标网址是否可访问")
        print("   4. DrissionPage库是否正确安装")
    
    print("\n" + "=" * 80)
    print("📚 商品参数提取完成")
    print("💡 成功提取了前十页的商品ID、SKUID、productSkcId和productJitMode参数")
    print("📋 所有参数信息已在控制台中详细显示")
    print("=" * 80)
